{"root": ["./src/main.ts", "./src/router.ts", "./src/vite-env.d.ts", "./src/stores/language.ts", "./src/stores/theme.ts", "./src/utils/deductivethinking.ts", "./src/utils/geminiapi.ts", "./src/utils/loadingstate.ts", "./src/utils/questiongenerator.ts", "./src/utils/systemicthinking.ts", "./src/app.vue", "./src/components/analysispage.vue", "./src/components/deductiveanalysispage.vue", "./src/components/deductivetreegraph.vue", "./src/components/historypanel.vue", "./src/components/languagetoggle.vue", "./src/components/modelsettings.vue", "./src/components/networkgraph.vue", "./src/components/questioncard.vue", "./src/components/themetoggle.vue", "./src/components/thinkingmodelselector.vue", "./src/components/thinkingtrainer.vue"], "version": "5.8.3"}